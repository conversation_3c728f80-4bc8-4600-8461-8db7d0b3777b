import * as THREE from 'three'

export interface FoldLine {
  id: string
  start: THREE.Vector3
  end: THREE.Vector3
  angle: number
  type: 'valley' | 'mountain'
  strength: number
  isActive: boolean
}

export interface Vertex {
  position: THREE.Vector3
  originalPosition: THREE.Vector3
  normal: THREE.Vector3
  index: number
  connectedFolds: string[]
}

export class FoldingPhysics {
  private vertices: Vertex[] = []
  private folds: Map<string, FoldLine> = new Map()
  private geometry: THREE.BufferGeometry
  private paperSize: number
  private subdivisions: number

  constructor(geometry: THREE.BufferGeometry, paperSize: number, subdivisions: number) {
    this.geometry = geometry
    this.paperSize = paperSize
    this.subdivisions = subdivisions
    this.initializeVertices()
  }

  private initializeVertices() {
    const positions = this.geometry.attributes.position.array as Float32Array
    const normals = this.geometry.attributes.normal.array as Float32Array
    
    this.vertices = []
    
    for (let i = 0; i < positions.length; i += 3) {
      const vertex: Vertex = {
        position: new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]),
        originalPosition: new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]),
        normal: new THREE.Vector3(normals[i], normals[i + 1], normals[i + 2]),
        index: i / 3,
        connectedFolds: []
      }
      this.vertices.push(vertex)
    }
  }

  public createFoldLine(start: THREE.Vector3, end: THREE.Vector3, type: 'valley' | 'mountain' = 'valley'): string {
    const id = `fold_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const fold: FoldLine = {
      id,
      start: start.clone(),
      end: end.clone(),
      angle: 0,
      type,
      strength: 1.0,
      isActive: true
    }
    
    this.folds.set(id, fold)
    this.updateVertexFoldConnections(fold)
    
    return id
  }

  private updateVertexFoldConnections(fold: FoldLine) {
    const foldDirection = fold.end.clone().sub(fold.start).normalize()
    const foldLength = fold.start.distanceTo(fold.end)
    
    this.vertices.forEach(vertex => {
      const distanceToFoldLine = this.distancePointToLine(
        vertex.originalPosition,
        fold.start,
        fold.end
      )
      
      // If vertex is close to the fold line, connect it
      if (distanceToFoldLine < 0.1) {
        if (!vertex.connectedFolds.includes(fold.id)) {
          vertex.connectedFolds.push(fold.id)
        }
      }
    })
  }

  private distancePointToLine(point: THREE.Vector3, lineStart: THREE.Vector3, lineEnd: THREE.Vector3): number {
    const lineDirection = lineEnd.clone().sub(lineStart)
    const lineLength = lineDirection.length()
    
    if (lineLength === 0) return point.distanceTo(lineStart)
    
    lineDirection.normalize()
    
    const pointToStart = point.clone().sub(lineStart)
    const projection = pointToStart.dot(lineDirection)
    
    // Clamp projection to line segment
    const clampedProjection = Math.max(0, Math.min(lineLength, projection))
    const closestPoint = lineStart.clone().add(lineDirection.multiplyScalar(clampedProjection))
    
    return point.distanceTo(closestPoint)
  }

  public animateFold(foldId: string, targetAngle: number, duration: number = 1000): Promise<void> {
    return new Promise((resolve) => {
      const fold = this.folds.get(foldId)
      if (!fold) {
        resolve()
        return
      }

      const startAngle = fold.angle
      const angleChange = targetAngle - startAngle
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        // Use easing function for smooth animation
        const easedProgress = this.easeInOutCubic(progress)
        
        fold.angle = startAngle + angleChange * easedProgress
        this.applyFold(fold)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          resolve()
        }
      }
      
      animate()
    })
  }

  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  private applyFold(fold: FoldLine) {
    const foldAxis = fold.end.clone().sub(fold.start).normalize()
    const foldCenter = fold.start.clone().add(fold.end).multiplyScalar(0.5)
    
    // Create rotation matrix for the fold
    const rotationMatrix = new THREE.Matrix4().makeRotationAxis(
      foldAxis,
      fold.angle * (fold.type === 'valley' ? 1 : -1)
    )
    
    this.vertices.forEach(vertex => {
      if (vertex.connectedFolds.includes(fold.id)) {
        // Determine which side of the fold line the vertex is on
        const sideOfFold = this.getSideOfFoldLine(vertex.originalPosition, fold)
        
        if (sideOfFold !== 0) {
          // Translate to fold center, rotate, then translate back
          const relativePosition = vertex.originalPosition.clone().sub(foldCenter)
          
          // Only rotate vertices on one side of the fold
          if (sideOfFold > 0) {
            relativePosition.applyMatrix4(rotationMatrix)
          }
          
          vertex.position.copy(relativePosition.add(foldCenter))
        }
      }
    })
    
    this.updateGeometry()
  }

  private getSideOfFoldLine(point: THREE.Vector3, fold: FoldLine): number {
    const foldDirection = fold.end.clone().sub(fold.start)
    const pointDirection = point.clone().sub(fold.start)
    
    // Use cross product to determine side (in 2D, we use the Z component)
    const cross = foldDirection.x * pointDirection.y - foldDirection.y * pointDirection.x
    
    return Math.sign(cross)
  }

  public updateGeometry() {
    const positions = this.geometry.attributes.position.array as Float32Array
    
    this.vertices.forEach((vertex, index) => {
      const i = index * 3
      positions[i] = vertex.position.x
      positions[i + 1] = vertex.position.y
      positions[i + 2] = vertex.position.z
    })
    
    this.geometry.attributes.position.needsUpdate = true
    this.geometry.computeVertexNormals()
  }

  public getFolds(): FoldLine[] {
    return Array.from(this.folds.values())
  }

  public removeFold(foldId: string) {
    const fold = this.folds.get(foldId)
    if (fold) {
      // Remove fold connections from vertices
      this.vertices.forEach(vertex => {
        const index = vertex.connectedFolds.indexOf(foldId)
        if (index > -1) {
          vertex.connectedFolds.splice(index, 1)
        }
      })
      
      this.folds.delete(foldId)
      this.resetToOriginalPositions()
      this.reapplyAllFolds()
    }
  }

  private resetToOriginalPositions() {
    this.vertices.forEach(vertex => {
      vertex.position.copy(vertex.originalPosition)
    })
  }

  private reapplyAllFolds() {
    this.folds.forEach(fold => {
      if (fold.isActive) {
        this.applyFold(fold)
      }
    })
  }

  public reset() {
    this.folds.clear()
    this.resetToOriginalPositions()
    this.updateGeometry()
    
    // Reset vertex fold connections
    this.vertices.forEach(vertex => {
      vertex.connectedFolds = []
    })
  }

  public setFoldStrength(foldId: string, strength: number) {
    const fold = this.folds.get(foldId)
    if (fold) {
      fold.strength = Math.max(0, Math.min(1, strength))
      this.reapplyAllFolds()
    }
  }

  public toggleFold(foldId: string) {
    const fold = this.folds.get(foldId)
    if (fold) {
      fold.isActive = !fold.isActive
      this.reapplyAllFolds()
    }
  }
}
