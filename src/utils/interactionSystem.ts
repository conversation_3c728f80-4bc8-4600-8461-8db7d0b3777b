import * as THREE from 'three'

export interface InteractionPoint {
  position: THREE.Vector3
  normal: THREE.Vector3
  uv: THREE.Vector2
  distance: number
}

export interface FoldPreview {
  start: THREE.Vector3
  end: THREE.Vector3
  isValid: boolean
  snapPoints: THREE.Vector3[]
}

export class InteractionSystem {
  private camera: THREE.Camera
  private raycaster: THREE.Raycaster
  private paperMesh: THREE.Mesh
  private snapDistance: number = 0.2
  private gridSize: number = 0.5

  constructor(camera: THREE.Camera, paperMesh: THREE.Mesh) {
    this.camera = camera
    this.paperMesh = paperMesh
    this.raycaster = new THREE.Raycaster()
  }

  public getIntersectionPoint(pointer: THREE.Vector2): InteractionPoint | null {
    this.raycaster.setFromCamera(pointer, this.camera)
    const intersects = this.raycaster.intersectObject(this.paperMesh)

    if (intersects.length > 0) {
      const intersection = intersects[0]
      return {
        position: intersection.point,
        normal: intersection.face?.normal || new THREE.Vector3(0, 0, 1),
        uv: intersection.uv || new THREE.Vector2(),
        distance: intersection.distance
      }
    }

    return null
  }

  public snapToGrid(point: THREE.Vector3): THREE.Vector3 {
    const snapped = point.clone()
    snapped.x = Math.round(snapped.x / this.gridSize) * this.gridSize
    snapped.y = Math.round(snapped.y / this.gridSize) * this.gridSize
    return snapped
  }

  public snapToVertex(point: THREE.Vector3): THREE.Vector3 {
    const geometry = this.paperMesh.geometry as THREE.BufferGeometry
    const positions = geometry.attributes.position.array as Float32Array
    
    let closestVertex = point.clone()
    let minDistance = this.snapDistance

    for (let i = 0; i < positions.length; i += 3) {
      const vertex = new THREE.Vector3(
        positions[i],
        positions[i + 1],
        positions[i + 2]
      )
      
      // Transform vertex to world space
      vertex.applyMatrix4(this.paperMesh.matrixWorld)
      
      const distance = point.distanceTo(vertex)
      if (distance < minDistance) {
        minDistance = distance
        closestVertex = vertex
      }
    }

    return closestVertex
  }

  public snapToEdge(point: THREE.Vector3): THREE.Vector3 {
    const geometry = this.paperMesh.geometry as THREE.BufferGeometry
    const positions = geometry.attributes.position.array as Float32Array
    const indices = geometry.index?.array

    if (!indices) return point

    let closestPoint = point.clone()
    let minDistance = this.snapDistance

    // Check all edges
    for (let i = 0; i < indices.length; i += 3) {
      const triangle = [
        indices[i] * 3,
        indices[i + 1] * 3,
        indices[i + 2] * 3
      ]

      // Check each edge of the triangle
      for (let j = 0; j < 3; j++) {
        const start = new THREE.Vector3(
          positions[triangle[j]],
          positions[triangle[j] + 1],
          positions[triangle[j] + 2]
        )
        const end = new THREE.Vector3(
          positions[triangle[(j + 1) % 3]],
          positions[triangle[(j + 1) % 3] + 1],
          positions[triangle[(j + 1) % 3] + 2]
        )

        // Transform to world space
        start.applyMatrix4(this.paperMesh.matrixWorld)
        end.applyMatrix4(this.paperMesh.matrixWorld)

        const edgePoint = this.closestPointOnLineSegment(point, start, end)
        const distance = point.distanceTo(edgePoint)

        if (distance < minDistance) {
          minDistance = distance
          closestPoint = edgePoint
        }
      }
    }

    return closestPoint
  }

  private closestPointOnLineSegment(point: THREE.Vector3, start: THREE.Vector3, end: THREE.Vector3): THREE.Vector3 {
    const line = end.clone().sub(start)
    const lineLength = line.length()
    
    if (lineLength === 0) return start.clone()
    
    line.normalize()
    
    const pointToStart = point.clone().sub(start)
    const projection = pointToStart.dot(line)
    
    // Clamp to line segment
    const clampedProjection = Math.max(0, Math.min(lineLength, projection))
    
    return start.clone().add(line.multiplyScalar(clampedProjection))
  }

  public createFoldPreview(startPoint: THREE.Vector3, currentPoint: THREE.Vector3): FoldPreview {
    const snappedStart = this.snapToVertex(startPoint)
    const snappedEnd = this.snapToVertex(currentPoint)
    
    // Generate snap points along the fold line
    const snapPoints: THREE.Vector3[] = []
    const direction = snappedEnd.clone().sub(snappedStart).normalize()
    const distance = snappedStart.distanceTo(snappedEnd)
    
    for (let i = 0; i <= Math.floor(distance / this.gridSize); i++) {
      const snapPoint = snappedStart.clone().add(
        direction.clone().multiplyScalar(i * this.gridSize)
      )
      snapPoints.push(snapPoint)
    }

    return {
      start: snappedStart,
      end: snappedEnd,
      isValid: this.isValidFoldLine(snappedStart, snappedEnd),
      snapPoints
    }
  }

  private isValidFoldLine(start: THREE.Vector3, end: THREE.Vector3): boolean {
    // Check minimum fold line length
    const minLength = 0.1
    if (start.distanceTo(end) < minLength) return false

    // Check if fold line is within paper bounds
    const paperSize = 5 // This should come from the store
    const halfSize = paperSize / 2

    const withinBounds = (point: THREE.Vector3) => {
      return Math.abs(point.x) <= halfSize && Math.abs(point.y) <= halfSize
    }

    return withinBounds(start) && withinBounds(end)
  }

  public getSymmetryLine(point: THREE.Vector3, type: 'horizontal' | 'vertical' | 'diagonal'): { start: THREE.Vector3, end: THREE.Vector3 } {
    const paperSize = 5 // This should come from the store
    const halfSize = paperSize / 2

    switch (type) {
      case 'horizontal':
        return {
          start: new THREE.Vector3(-halfSize, point.y, 0),
          end: new THREE.Vector3(halfSize, point.y, 0)
        }
      case 'vertical':
        return {
          start: new THREE.Vector3(point.x, -halfSize, 0),
          end: new THREE.Vector3(point.x, halfSize, 0)
        }
      case 'diagonal':
        // Create diagonal line through the point
        const slope = point.y / point.x
        const yAtLeft = slope * (-halfSize)
        const yAtRight = slope * halfSize
        
        return {
          start: new THREE.Vector3(-halfSize, yAtLeft, 0),
          end: new THREE.Vector3(halfSize, yAtRight, 0)
        }
      default:
        return {
          start: point.clone(),
          end: point.clone()
        }
    }
  }

  public findIntersections(foldLine: { start: THREE.Vector3, end: THREE.Vector3 }): THREE.Vector3[] {
    const intersections: THREE.Vector3[] = []
    const geometry = this.paperMesh.geometry as THREE.BufferGeometry
    const positions = geometry.attributes.position.array as Float32Array
    const indices = geometry.index?.array

    if (!indices) return intersections

    // Check intersections with all edges
    for (let i = 0; i < indices.length; i += 3) {
      const triangle = [
        indices[i] * 3,
        indices[i + 1] * 3,
        indices[i + 2] * 3
      ]

      for (let j = 0; j < 3; j++) {
        const edgeStart = new THREE.Vector3(
          positions[triangle[j]],
          positions[triangle[j] + 1],
          positions[triangle[j] + 2]
        )
        const edgeEnd = new THREE.Vector3(
          positions[triangle[(j + 1) % 3]],
          positions[triangle[(j + 1) % 3] + 1],
          positions[triangle[(j + 1) % 3] + 2]
        )

        const intersection = this.lineIntersection2D(
          foldLine.start, foldLine.end,
          edgeStart, edgeEnd
        )

        if (intersection) {
          intersections.push(intersection)
        }
      }
    }

    return intersections
  }

  private lineIntersection2D(
    line1Start: THREE.Vector3, line1End: THREE.Vector3,
    line2Start: THREE.Vector3, line2End: THREE.Vector3
  ): THREE.Vector3 | null {
    const x1 = line1Start.x, y1 = line1Start.y
    const x2 = line1End.x, y2 = line1End.y
    const x3 = line2Start.x, y3 = line2Start.y
    const x4 = line2End.x, y4 = line2End.y

    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
    
    if (Math.abs(denom) < 1e-10) return null // Lines are parallel

    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom

    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return new THREE.Vector3(
        x1 + t * (x2 - x1),
        y1 + t * (y2 - y1),
        0
      )
    }

    return null
  }

  public setSnapDistance(distance: number) {
    this.snapDistance = distance
  }

  public setGridSize(size: number) {
    this.gridSize = size
  }
}
