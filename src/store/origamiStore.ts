import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import * as THREE from 'three'

export interface Fold {
  id: string
  startPoint: THREE.Vector3
  endPoint: THREE.Vector3
  angle: number
  type: 'valley' | 'mountain'
  timestamp: number
}

export interface PaperState {
  vertices: Float32Array
  normals: Float32Array
  folds: Fold[]
}

interface OrigamiStore {
  // Paper properties
  paperSize: number
  setPaperSize: (size: number) => void
  
  subdivisions: number
  setSubdivisions: (subdivisions: number) => void
  
  paperThickness: number
  setPaperThickness: (thickness: number) => void
  
  // Visual settings
  showCreases: boolean
  setShowCreases: (show: boolean) => void
  
  foldStrength: number
  setFoldStrength: (strength: number) => void
  
  // Interaction mode
  currentMode: 'fold' | 'view'
  setCurrentMode: (mode: 'fold' | 'view') => void
  
  // Fold management
  foldHistory: Fold[]
  redoHistory: Fold[]
  addFold: (fold: Fold) => void
  undoFold: () => void
  redoFold: () => void
  resetPaper: () => void
  
  // Paper state
  currentPaperState: PaperState | null
  setPaperState: (state: PaperState) => void
  
  // Save/Load functionality
  savedStates: { [key: string]: PaperState }
  savePaper: (name?: string) => void
  loadPaper: (name?: string) => void
  
  // Animation
  isAnimating: boolean
  setIsAnimating: (animating: boolean) => void
  
  animationSpeed: number
  setAnimationSpeed: (speed: number) => void
}

export const useOrigamiStore = create<OrigamiStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial paper properties
    paperSize: 5,
    setPaperSize: (size) => set({ paperSize: size }),
    
    subdivisions: 30,
    setSubdivisions: (subdivisions) => set({ subdivisions }),
    
    paperThickness: 0.002,
    setPaperThickness: (thickness) => set({ paperThickness: thickness }),
    
    // Visual settings
    showCreases: true,
    setShowCreases: (show) => set({ showCreases: show }),
    
    foldStrength: 0.8,
    setFoldStrength: (strength) => set({ foldStrength: strength }),
    
    // Interaction mode
    currentMode: 'fold',
    setCurrentMode: (mode) => set({ currentMode: mode }),
    
    // Fold management
    foldHistory: [],
    redoHistory: [],
    
    addFold: (fold) => set((state) => ({
      foldHistory: [...state.foldHistory, fold],
      redoHistory: [] // Clear redo history when new fold is added
    })),
    
    undoFold: () => set((state) => {
      if (state.foldHistory.length === 0) return state
      
      const lastFold = state.foldHistory[state.foldHistory.length - 1]
      const newHistory = state.foldHistory.slice(0, -1)
      const newRedoHistory = [...state.redoHistory, lastFold]
      
      return {
        foldHistory: newHistory,
        redoHistory: newRedoHistory
      }
    }),
    
    redoFold: () => set((state) => {
      if (state.redoHistory.length === 0) return state
      
      const lastRedo = state.redoHistory[state.redoHistory.length - 1]
      const newRedoHistory = state.redoHistory.slice(0, -1)
      const newHistory = [...state.foldHistory, lastRedo]
      
      return {
        foldHistory: newHistory,
        redoHistory: newRedoHistory
      }
    }),
    
    resetPaper: () => set({
      foldHistory: [],
      redoHistory: [],
      currentPaperState: null
    }),
    
    // Paper state
    currentPaperState: null,
    setPaperState: (state) => set({ currentPaperState: state }),
    
    // Save/Load functionality
    savedStates: {},
    
    savePaper: (name = 'default') => set((state) => {
      if (!state.currentPaperState) return state
      
      const savedStates = {
        ...state.savedStates,
        [name]: { ...state.currentPaperState }
      }
      
      // Also save to localStorage
      try {
        localStorage.setItem('origami-saved-states', JSON.stringify(savedStates))
      } catch (error) {
        console.warn('Failed to save to localStorage:', error)
      }
      
      return { savedStates }
    }),
    
    loadPaper: (name = 'default') => set((state) => {
      // Try to load from localStorage first
      try {
        const saved = localStorage.getItem('origami-saved-states')
        if (saved) {
          const parsedStates = JSON.parse(saved)
          if (parsedStates[name]) {
            return {
              currentPaperState: parsedStates[name],
              savedStates: { ...state.savedStates, ...parsedStates }
            }
          }
        }
      } catch (error) {
        console.warn('Failed to load from localStorage:', error)
      }
      
      // Fallback to in-memory saved states
      const savedState = state.savedStates[name]
      if (savedState) {
        return { currentPaperState: { ...savedState } }
      }
      
      return state
    }),
    
    // Animation
    isAnimating: false,
    setIsAnimating: (animating) => set({ isAnimating: animating }),
    
    animationSpeed: 1.0,
    setAnimationSpeed: (speed) => set({ animationSpeed: speed })
  }))
)

// Subscribe to changes and auto-save
useOrigamiStore.subscribe(
  (state) => state.foldHistory,
  (foldHistory) => {
    // Auto-save fold history to localStorage
    try {
      localStorage.setItem('origami-fold-history', JSON.stringify(foldHistory))
    } catch (error) {
      console.warn('Failed to auto-save fold history:', error)
    }
  }
)

// Load fold history on initialization
try {
  const savedHistory = localStorage.getItem('origami-fold-history')
  if (savedHistory) {
    const parsedHistory = JSON.parse(savedHistory)
    useOrigamiStore.setState({ foldHistory: parsedHistory })
  }
} catch (error) {
  console.warn('Failed to load fold history:', error)
}
