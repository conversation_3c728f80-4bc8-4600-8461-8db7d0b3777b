.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* UI Overlay Styles */
.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 100;
}

.ui-panel {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  color: white;
  pointer-events: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.ui-panel h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.controls-panel {
  top: 20px;
  left: 20px;
  min-width: 200px;
}

.info-panel {
  top: 20px;
  right: 20px;
  min-width: 250px;
}

.actions-panel {
  bottom: 20px;
  left: 20px;
  display: flex;
  gap: 10px;
}

.btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn.primary {
  background: rgba(103, 126, 234, 0.8);
  border-color: rgba(103, 126, 234, 1);
}

.btn.primary:hover {
  background: rgba(103, 126, 234, 1);
}

.btn.danger {
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(239, 68, 68, 1);
}

.btn.danger:hover {
  background: rgba(239, 68, 68, 1);
}

.slider-container {
  margin: 10px 0;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.fold-info {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 5px 0;
}

.fold-info strong {
  color: white;
}

.fold-history {
  margin-top: 15px;
}

.mode-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 5px;
  border-radius: 4px;
  width: 100%;
}

.mode-select option {
  background: #1a1a1a;
  color: white;
}

/* Controls component styles */
.controls-info {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 300px;
  font-size: 12px;
}

.controls-info h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #667eea;
}

.shortcut-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shortcut {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shortcut kbd {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  font-family: monospace;
  color: #fff;
}

.shortcut span {
  color: rgba(255, 255, 255, 0.8);
}

.current-mode, .fold-count {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 11px;
}

/* Presets panel styles */
.presets-panel {
  position: absolute;
  top: 20px;
  left: 250px;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.presets-panel h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
}

.presets-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.preset-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.preset-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.preset-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.preset-icon {
  font-size: 16px;
}

.preset-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  flex: 1;
}

.difficulty-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
}

.preset-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.preset-stats {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 10px;
}

.preset-btn {
  width: 100%;
  padding: 6px 12px;
  font-size: 12px;
  background: rgba(103, 126, 234, 0.8);
  border-color: rgba(103, 126, 234, 1);
}

.preset-btn:hover {
  background: rgba(103, 126, 234, 1);
}

/* Responsive design */
@media (max-width: 768px) {
  .ui-panel {
    padding: 15px;
    min-width: 180px;
  }

  .controls-info {
    position: relative;
    margin-top: 10px;
    max-width: none;
  }

  .shortcut-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
  }

  .presets-panel {
    position: relative;
    left: 0;
    margin-top: 10px;
    max-width: none;
  }

  .presets-grid {
    grid-template-columns: 1fr;
  }
}
