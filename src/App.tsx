import React from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Environment, Grid } from '@react-three/drei'
import { Leva } from 'leva'
import OrigamiPaper from './components/OrigamiPaper'
import UI from './components/UI'
import { PaperLighting } from './components/PaperMaterial'
import PostProcessing from './components/PostProcessing'
import './App.css'

function App() {
  return (
    <div className="app">
      <div className="canvas-container">
        <Canvas
          camera={{ position: [0, 5, 10], fov: 50 }}
          shadows="soft"
          gl={{
            antialias: true,
            alpha: false,
            powerPreference: "high-performance",
            stencil: false,
            depth: true
          }}
          dpr={[1, 2]}
        >
          {/* Enhanced lighting system */}
          <PaperLighting />

          {/* Environment */}
          <Environment
            preset="studio"
            background={false}
            environmentIntensity={0.4}
          />

          <Grid
            args={[20, 20]}
            position={[0, -0.01, 0]}
            cellSize={1}
            cellThickness={0.5}
            cellColor="#6f6f6f"
            sectionSize={5}
            sectionThickness={1}
            sectionColor="#9d4b4b"
            fadeDistance={30}
            fadeStrength={1}
            followCamera={false}
            infiniteGrid={true}
          />

          {/* Main origami paper */}
          <OrigamiPaper />

          {/* Camera controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={3}
            maxDistance={50}
            target={[0, 0, 0]}
            enableDamping={true}
            dampingFactor={0.05}
            rotateSpeed={0.5}
            zoomSpeed={0.8}
            panSpeed={0.8}
          />

          {/* Post-processing effects */}
          <PostProcessing enabled={true} />
        </Canvas>
      </div>

      {/* UI Controls */}
      <UI />

      {/* Leva debug panel */}
      <Leva collapsed />
    </div>
  )
}

export default App
