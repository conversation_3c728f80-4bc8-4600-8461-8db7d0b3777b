import React, { useEffect, useCallback } from 'react'
import { useOrigamiStore } from '../store/origamiStore'

interface ControlsProps {
  onUndo: () => void
  onRedo: () => void
  onReset: () => void
  onSave: () => void
  onLoad: () => void
  onToggleMode: () => void
}

const Controls: React.FC<ControlsProps> = ({
  onUndo,
  onRedo,
  onReset,
  onSave,
  onLoad,
  onToggleMode
}) => {
  const { currentMode, foldHistory } = useOrigamiStore()

  // Keyboard shortcuts
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Prevent default browser shortcuts when our app is focused
    if (event.target === document.body || (event.target as HTMLElement).closest('.app')) {
      switch (event.key.toLowerCase()) {
        case 'z':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            if (event.shiftKey) {
              onRedo()
            } else {
              onUndo()
            }
          }
          break
        
        case 'r':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            onReset()
          }
          break
        
        case 's':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            onSave()
          }
          break
        
        case 'o':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            onLoad()
          }
          break
        
        case 'tab':
          event.preventDefault()
          onToggleMode()
          break
        
        case 'escape':
          event.preventDefault()
          // Cancel current operation or switch to view mode
          useOrigamiStore.getState().setCurrentMode('view')
          break
        
        case 'f':
          if (!event.ctrlKey && !event.metaKey) {
            event.preventDefault()
            useOrigamiStore.getState().setCurrentMode('fold')
          }
          break
        
        case 'v':
          if (!event.ctrlKey && !event.metaKey) {
            event.preventDefault()
            useOrigamiStore.getState().setCurrentMode('view')
          }
          break
        
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
          if (!event.ctrlKey && !event.metaKey) {
            event.preventDefault()
            const strength = parseInt(event.key) / 5
            useOrigamiStore.getState().setFoldStrength(strength)
          }
          break
        
        case 'c':
          if (!event.ctrlKey && !event.metaKey) {
            event.preventDefault()
            const { showCreases, setShowCreases } = useOrigamiStore.getState()
            setShowCreases(!showCreases)
          }
          break
      }
    }
  }, [onUndo, onRedo, onReset, onSave, onLoad, onToggleMode])

  // Mouse gesture detection
  const handleMouseMove = useCallback((event: MouseEvent) => {
    // Implement gesture detection here if needed
    // For now, we'll keep it simple and rely on the OrigamiPaper component
  }, [])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousemove', handleMouseMove)
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [handleKeyDown, handleMouseMove])

  return (
    <div className="controls-info">
      <div className="keyboard-shortcuts">
        <h4>Keyboard Shortcuts</h4>
        <div className="shortcut-list">
          <div className="shortcut">
            <kbd>Tab</kbd> <span>Toggle Mode</span>
          </div>
          <div className="shortcut">
            <kbd>F</kbd> <span>Fold Mode</span>
          </div>
          <div className="shortcut">
            <kbd>V</kbd> <span>View Mode</span>
          </div>
          <div className="shortcut">
            <kbd>Ctrl+Z</kbd> <span>Undo</span>
          </div>
          <div className="shortcut">
            <kbd>Ctrl+Shift+Z</kbd> <span>Redo</span>
          </div>
          <div className="shortcut">
            <kbd>Ctrl+R</kbd> <span>Reset</span>
          </div>
          <div className="shortcut">
            <kbd>Ctrl+S</kbd> <span>Save</span>
          </div>
          <div className="shortcut">
            <kbd>Ctrl+O</kbd> <span>Load</span>
          </div>
          <div className="shortcut">
            <kbd>C</kbd> <span>Toggle Creases</span>
          </div>
          <div className="shortcut">
            <kbd>1-5</kbd> <span>Fold Strength</span>
          </div>
          <div className="shortcut">
            <kbd>Esc</kbd> <span>Cancel/View Mode</span>
          </div>
        </div>
      </div>
      
      <div className="current-mode">
        <strong>Current Mode:</strong> {currentMode === 'fold' ? 'Folding' : 'Viewing'}
      </div>
      
      <div className="fold-count">
        <strong>Folds:</strong> {foldHistory.length}
      </div>
    </div>
  )
}

export default Controls
