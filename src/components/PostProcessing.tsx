import React from 'react'
import { 
  <PERSON><PERSON><PERSON>pose<PERSON>, 
  SSAO, 
  Bloom, 
  ToneMapping, 
  Vignette,
  ChromaticAberration,
  DepthOfField
} from '@react-three/postprocessing'
import { BlendFunction, ToneMappingMode } from 'postprocessing'

interface PostProcessingProps {
  enabled?: boolean
}

const PostProcessing: React.FC<PostProcessingProps> = ({ enabled = true }) => {
  if (!enabled) return null

  return (
    <EffectComposer>
      {/* Screen Space Ambient Occlusion for realistic shadows */}
      <SSAO
        blendFunction={BlendFunction.MULTIPLY}
        samples={16}
        rings={4}
        distanceThreshold={0.4}
        distanceFalloff={0.0}
        rangeThreshold={0.0015}
        rangeFalloff={0.01}
        luminanceInfluence={0.7}
        radius={0.1}
        scale={1.0}
        bias={0.025}
      />
      
      {/* Subtle bloom for paper highlights */}
      <Bloom
        blendFunction={BlendFunction.ADD}
        intensity={0.3}
        width={300}
        height={300}
        kernelSize={5}
        luminanceThreshold={0.9}
        luminanceSmoothing={0.4}
      />
      
      {/* Tone mapping for realistic lighting */}
      <ToneMapping
        blendFunction={BlendFunction.NORMAL}
        adaptive={true}
        mode={ToneMappingMode.ACES_FILMIC}
        resolution={256}
        whitePoint={4.0}
        middleGrey={0.6}
        minLuminance={0.01}
        averageLuminance={0.01}
        adaptationRate={1.0}
      />
      
      {/* Subtle vignette for focus */}
      <Vignette
        blendFunction={BlendFunction.NORMAL}
        eskil={false}
        offset={0.1}
        darkness={0.2}
      />
      
      {/* Very subtle chromatic aberration for realism */}
      <ChromaticAberration
        blendFunction={BlendFunction.NORMAL}
        offset={[0.0005, 0.0005]}
      />
      
      {/* Optional depth of field for focus effects */}
      <DepthOfField
        blendFunction={BlendFunction.NORMAL}
        focusDistance={0.02}
        focalLength={0.5}
        bokehScale={1.0}
        height={700}
      />
    </EffectComposer>
  )
}

export default PostProcessing
