import React, { useRef, useMemo, useState, useCallback, useEffect } from 'react'
import { use<PERSON>rame, useThree } from '@react-three/fiber'
import { useGesture } from '@use-gesture/react'
import * as THREE from 'three'
import { useOrigamiStore } from '../store/origamiStore'
import { FoldingPhysics, FoldLine } from '../utils/foldingPhysics'
import { InteractionSystem, FoldPreview } from '../utils/interactionSystem'
import PaperMaterial, { CreaseLines } from './PaperMaterial'

const OrigamiPaper: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null)
  const { camera, pointer } = useThree()
  const [isDragging, setIsDragging] = useState(false)
  const [foldStart, setFoldStart] = useState<THREE.Vector3 | null>(null)
  const [foldPreview, setFoldPreview] = useState<FoldPreview | null>(null)
  const [foldingPhysics, setFoldingPhysics] = useState<FoldingPhysics | null>(null)
  const [interactionSystem, setInteractionSystem] = useState<InteractionSystem | null>(null)

  const {
    paperSize,
    subdivisions,
    paperThickness,
    showCreases,
    foldStrength,
    currentMode,
    addFold,
    undoFold,
    resetPaper
  } = useOrigamiStore()

  // Create paper geometry with subdivisions for realistic folding
  const geometry = useMemo(() => {
    const geo = new THREE.PlaneGeometry(
      paperSize,
      paperSize,
      subdivisions,
      subdivisions
    )

    // Add some initial slight randomness to make it feel more natural
    const positions = geo.attributes.position.array as Float32Array
    for (let i = 0; i < positions.length; i += 3) {
      positions[i + 2] += (Math.random() - 0.5) * 0.001 // Very slight Z variation
    }

    geo.attributes.position.needsUpdate = true
    geo.computeVertexNormals()

    return geo
  }, [paperSize, subdivisions])

  // Initialize physics and interaction systems when geometry changes
  useEffect(() => {
    if (meshRef.current && geometry) {
      const physics = new FoldingPhysics(geometry, paperSize, subdivisions)
      const interaction = new InteractionSystem(camera, meshRef.current)

      setFoldingPhysics(physics)
      setInteractionSystem(interaction)
    }
  }, [geometry, paperSize, subdivisions, camera])

  // Enhanced paper material
  const paperMaterial = useMemo(() => {
    return <PaperMaterial geometry={geometry} />
  }, [geometry])

  // Handle mouse interactions for folding
  const handlePointerDown = useCallback((event: THREE.Event) => {
    if (currentMode !== 'fold' || !interactionSystem) return

    event.stopPropagation()
    setIsDragging(true)

    const intersectionPoint = interactionSystem.getIntersectionPoint(pointer)
    if (intersectionPoint) {
      const snappedPoint = interactionSystem.snapToVertex(intersectionPoint.position)
      setFoldStart(snappedPoint)
    }
  }, [currentMode, interactionSystem, pointer])

  const handlePointerMove = useCallback((event: THREE.Event) => {
    if (!isDragging || !foldStart || !interactionSystem || currentMode !== 'fold') return

    event.stopPropagation()

    const intersectionPoint = interactionSystem.getIntersectionPoint(pointer)
    if (intersectionPoint) {
      const preview = interactionSystem.createFoldPreview(foldStart, intersectionPoint.position)
      setFoldPreview(preview)
    }
  }, [isDragging, foldStart, interactionSystem, currentMode, pointer])

  const handlePointerUp = useCallback(() => {
    if (foldStart && foldPreview && foldPreview.isValid && foldingPhysics) {
      // Create the fold
      const foldId = foldingPhysics.createFoldLine(
        foldPreview.start,
        foldPreview.end,
        'valley' // Default to valley fold
      )

      // Animate the fold
      foldingPhysics.animateFold(foldId, Math.PI * 0.5 * foldStrength, 800)

      // Add to store
      addFold({
        id: foldId,
        startPoint: foldPreview.start,
        endPoint: foldPreview.end,
        angle: Math.PI * 0.5 * foldStrength,
        type: 'valley',
        timestamp: Date.now()
      })
    }

    setIsDragging(false)
    setFoldStart(null)
    setFoldPreview(null)
  }, [foldStart, foldPreview, foldingPhysics, foldStrength, addFold])

  // Handle reset from store
  useEffect(() => {
    if (foldingPhysics) {
      foldingPhysics.reset()
    }
  }, [resetPaper, foldingPhysics])

  // Update fold strength when it changes
  useEffect(() => {
    if (foldingPhysics) {
      const folds = foldingPhysics.getFolds()
      folds.forEach(fold => {
        foldingPhysics.setFoldStrength(fold.id, foldStrength)
      })
    }
  }, [foldStrength, foldingPhysics])

  // Gesture handling for touch devices and advanced interactions
  const bind = useGesture({
    onDrag: ({ active, movement: [mx, my], first, last, event }) => {
      if (currentMode !== 'fold' || !interactionSystem) return

      event?.stopPropagation()

      if (first) {
        // Start of drag - same as pointer down
        const intersectionPoint = interactionSystem.getIntersectionPoint(pointer)
        if (intersectionPoint) {
          const snappedPoint = interactionSystem.snapToVertex(intersectionPoint.position)
          setFoldStart(snappedPoint)
          setIsDragging(true)
        }
      } else if (active && foldStart) {
        // During drag - same as pointer move
        const intersectionPoint = interactionSystem.getIntersectionPoint(pointer)
        if (intersectionPoint) {
          const preview = interactionSystem.createFoldPreview(foldStart, intersectionPoint.position)
          setFoldPreview(preview)
        }
      } else if (last) {
        // End of drag - same as pointer up
        if (foldStart && foldPreview && foldPreview.isValid && foldingPhysics) {
          const foldId = foldingPhysics.createFoldLine(
            foldPreview.start,
            foldPreview.end,
            'valley'
          )

          foldingPhysics.animateFold(foldId, Math.PI * 0.5 * foldStrength, 800)

          addFold({
            id: foldId,
            startPoint: foldPreview.start,
            endPoint: foldPreview.end,
            angle: Math.PI * 0.5 * foldStrength,
            type: 'valley',
            timestamp: Date.now()
          })
        }

        setIsDragging(false)
        setFoldStart(null)
        setFoldPreview(null)
      }
    },
    onPinch: ({ offset: [scale], first, last }) => {
      // Handle pinch-to-zoom on touch devices
      if (first) {
        // Store initial camera position
      } else if (last) {
        // Finalize zoom
      } else {
        // Apply zoom - this would typically be handled by OrbitControls
        // but we can add custom behavior here if needed
      }
    },
    onWheel: ({ delta: [, dy] }) => {
      // Custom wheel handling if needed
      // OrbitControls handles this by default
    }
  })

  return (
    <group>
      {/* Main paper mesh */}
      <mesh
        ref={meshRef}
        geometry={geometry}
        receiveShadow
        castShadow
        onPointerDown={handlePointerDown}
        onPointerMove={handlePointerMove}
        onPointerUp={handlePointerUp}
        {...bind()}
      >
        {paperMaterial}
      </mesh>

      {/* Enhanced crease lines visualization */}
      <CreaseLines folds={foldingPhysics ? foldingPhysics.getFolds() : []} />

      {/* Fold preview during creation */}
      {foldPreview && isDragging && (
        <line>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={2}
              array={new Float32Array([
                foldPreview.start.x, foldPreview.start.y, foldPreview.start.z + 0.003,
                foldPreview.end.x, foldPreview.end.y, foldPreview.end.z + 0.003
              ])}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial
            color={foldPreview.isValid ? '#00ff00' : '#ff0000'}
            linewidth={3}
            transparent
            opacity={0.8}
          />
        </line>
      )}

      {/* Snap points visualization */}
      {foldPreview && isDragging && foldPreview.snapPoints.map((point, index) => (
        <mesh key={index} position={[point.x, point.y, point.z + 0.004]}>
          <sphereGeometry args={[0.02, 8, 8]} />
          <meshBasicMaterial
            color="#ffff00"
            transparent
            opacity={0.6}
          />
        </mesh>
      ))}
    </group>
  )
}

export default OrigamiPaper
