import React from 'react'
import * as THREE from 'three'
import { useOrigamiStore } from '../store/origamiStore'

interface OrigamiPreset {
  id: string
  name: string
  description: string
  folds: Array<{
    start: THREE.Vector3
    end: THREE.Vector3
    type: 'valley' | 'mountain'
    angle: number
  }>
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  category: 'animals' | 'flowers' | 'geometric' | 'classic'
}

const origamiPresets: OrigamiPreset[] = [
  {
    id: 'simple-valley',
    name: 'Simple Valley Fold',
    description: 'A basic valley fold down the center',
    difficulty: 'beginner',
    category: 'classic',
    folds: [
      {
        start: new THREE.Vector3(0, -2.5, 0),
        end: new THREE.Vector3(0, 2.5, 0),
        type: 'valley',
        angle: Math.PI * 0.3
      }
    ]
  },
  {
    id: 'book-fold',
    name: 'Book Fold',
    description: 'Fold the paper in half like a book',
    difficulty: 'beginner',
    category: 'classic',
    folds: [
      {
        start: new THREE.Vector3(-2.5, 0, 0),
        end: new THREE.Vector3(2.5, 0, 0),
        type: 'valley',
        angle: Math.PI * 0.5
      }
    ]
  },
  {
    id: 'diagonal-fold',
    name: 'Diagonal Fold',
    description: 'A diagonal valley fold from corner to corner',
    difficulty: 'beginner',
    category: 'geometric',
    folds: [
      {
        start: new THREE.Vector3(-2.5, -2.5, 0),
        end: new THREE.Vector3(2.5, 2.5, 0),
        type: 'valley',
        angle: Math.PI * 0.4
      }
    ]
  },
  {
    id: 'preliminary-base',
    name: 'Preliminary Base',
    description: 'Foundation for many origami models',
    difficulty: 'intermediate',
    category: 'classic',
    folds: [
      {
        start: new THREE.Vector3(0, -2.5, 0),
        end: new THREE.Vector3(0, 2.5, 0),
        type: 'valley',
        angle: Math.PI * 0.5
      },
      {
        start: new THREE.Vector3(-2.5, 0, 0),
        end: new THREE.Vector3(2.5, 0, 0),
        type: 'valley',
        angle: Math.PI * 0.5
      },
      {
        start: new THREE.Vector3(-2.5, -2.5, 0),
        end: new THREE.Vector3(2.5, 2.5, 0),
        type: 'mountain',
        angle: Math.PI * 0.3
      },
      {
        start: new THREE.Vector3(-2.5, 2.5, 0),
        end: new THREE.Vector3(2.5, -2.5, 0),
        type: 'mountain',
        angle: Math.PI * 0.3
      }
    ]
  },
  {
    id: 'waterbomb-base',
    name: 'Waterbomb Base',
    description: 'Another fundamental origami base',
    difficulty: 'intermediate',
    category: 'classic',
    folds: [
      {
        start: new THREE.Vector3(0, -2.5, 0),
        end: new THREE.Vector3(0, 2.5, 0),
        type: 'mountain',
        angle: Math.PI * 0.4
      },
      {
        start: new THREE.Vector3(-2.5, 0, 0),
        end: new THREE.Vector3(2.5, 0, 0),
        type: 'mountain',
        angle: Math.PI * 0.4
      },
      {
        start: new THREE.Vector3(-2.5, -2.5, 0),
        end: new THREE.Vector3(2.5, 2.5, 0),
        type: 'valley',
        angle: Math.PI * 0.5
      },
      {
        start: new THREE.Vector3(-2.5, 2.5, 0),
        end: new THREE.Vector3(2.5, -2.5, 0),
        type: 'valley',
        angle: Math.PI * 0.5
      }
    ]
  },
  {
    id: 'windmill',
    name: 'Windmill Pattern',
    description: 'A decorative windmill fold pattern',
    difficulty: 'advanced',
    category: 'geometric',
    folds: [
      {
        start: new THREE.Vector3(-1.25, -2.5, 0),
        end: new THREE.Vector3(-1.25, 2.5, 0),
        type: 'valley',
        angle: Math.PI * 0.25
      },
      {
        start: new THREE.Vector3(1.25, -2.5, 0),
        end: new THREE.Vector3(1.25, 2.5, 0),
        type: 'mountain',
        angle: Math.PI * 0.25
      },
      {
        start: new THREE.Vector3(-2.5, -1.25, 0),
        end: new THREE.Vector3(2.5, -1.25, 0),
        type: 'mountain',
        angle: Math.PI * 0.25
      },
      {
        start: new THREE.Vector3(-2.5, 1.25, 0),
        end: new THREE.Vector3(2.5, 1.25, 0),
        type: 'valley',
        angle: Math.PI * 0.25
      }
    ]
  }
]

interface OrigamiPresetsProps {
  onApplyPreset: (preset: OrigamiPreset) => void
}

const OrigamiPresets: React.FC<OrigamiPresetsProps> = ({ onApplyPreset }) => {
  const { resetPaper } = useOrigamiStore()

  const handleApplyPreset = (preset: OrigamiPreset) => {
    // Reset paper first
    resetPaper()
    
    // Apply preset after a short delay to ensure reset is complete
    setTimeout(() => {
      onApplyPreset(preset)
    }, 100)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '#4ade80'
      case 'intermediate': return '#fbbf24'
      case 'advanced': return '#f87171'
      default: return '#6b7280'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'animals': return '🐾'
      case 'flowers': return '🌸'
      case 'geometric': return '📐'
      case 'classic': return '📜'
      default: return '📄'
    }
  }

  return (
    <div className="presets-panel">
      <h3>Origami Presets</h3>
      <div className="presets-grid">
        {origamiPresets.map(preset => (
          <div key={preset.id} className="preset-card">
            <div className="preset-header">
              <span className="preset-icon">{getCategoryIcon(preset.category)}</span>
              <h4>{preset.name}</h4>
              <span 
                className="difficulty-badge"
                style={{ backgroundColor: getDifficultyColor(preset.difficulty) }}
              >
                {preset.difficulty}
              </span>
            </div>
            
            <p className="preset-description">{preset.description}</p>
            
            <div className="preset-stats">
              <span className="fold-count">{preset.folds.length} folds</span>
              <span className="category">{preset.category}</span>
            </div>
            
            <button
              type="button"
              className="btn preset-btn"
              onClick={() => handleApplyPreset(preset)}
            >
              Apply Preset
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}

export default OrigamiPresets
export { origamiPresets }
export type { OrigamiPreset }
