import React, { useMemo, useRef } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'
import { useOrigamiStore } from '../store/origamiStore'

interface PaperMaterialProps {
  geometry: THREE.BufferGeometry
}

const PaperMaterial: React.FC<PaperMaterialProps> = ({ geometry }) => {
  const materialRef = useRef<THREE.MeshPhysicalMaterial>(null)
  const { paperThickness, showCreases } = useOrigamiStore()

  // Create custom shader material for realistic paper appearance
  const paperMaterial = useMemo(() => {
    const material = new THREE.MeshPhysicalMaterial({
      // Base paper color - slightly off-white with warm tint
      color: new THREE.Color('#fefefe'),
      
      // Physical properties for realistic paper
      roughness: 0.9,
      metalness: 0.0,
      clearcoat: 0.05,
      clearcoatRoughness: 0.95,
      
      // Paper thickness and transmission
      thickness: paperThickness * 100,
      transmission: 0.02,
      ior: 1.4,
      
      // Enable both sides
      side: THREE.DoubleSide,
      
      // Slight transparency for realism
      transparent: true,
      opacity: 0.98,
      
      // Enable shadows
      shadowSide: THREE.DoubleSide,
    })

    // Add subtle paper texture
    const textureLoader = new THREE.TextureLoader()
    
    // Create a procedural paper texture
    const canvas = document.createElement('canvas')
    canvas.width = 512
    canvas.height = 512
    const ctx = canvas.getContext('2d')!
    
    // Base paper color
    ctx.fillStyle = '#fefefe'
    ctx.fillRect(0, 0, 512, 512)
    
    // Add paper fiber texture
    ctx.globalAlpha = 0.03
    for (let i = 0; i < 1000; i++) {
      ctx.fillStyle = Math.random() > 0.5 ? '#f8f8f8' : '#ffffff'
      ctx.fillRect(
        Math.random() * 512,
        Math.random() * 512,
        Math.random() * 3 + 1,
        Math.random() * 20 + 5
      )
    }
    
    // Add subtle grain
    ctx.globalAlpha = 0.02
    for (let i = 0; i < 2000; i++) {
      ctx.fillStyle = Math.random() > 0.5 ? '#f0f0f0' : '#ffffff'
      ctx.fillRect(
        Math.random() * 512,
        Math.random() * 512,
        1,
        1
      )
    }
    
    const paperTexture = new THREE.CanvasTexture(canvas)
    paperTexture.wrapS = THREE.RepeatWrapping
    paperTexture.wrapT = THREE.RepeatWrapping
    paperTexture.repeat.set(2, 2)
    
    material.map = paperTexture
    
    // Create normal map for paper texture
    const normalCanvas = document.createElement('canvas')
    normalCanvas.width = 512
    normalCanvas.height = 512
    const normalCtx = normalCanvas.getContext('2d')!
    
    // Base normal (pointing up)
    normalCtx.fillStyle = '#8080ff'
    normalCtx.fillRect(0, 0, 512, 512)
    
    // Add subtle normal variations
    normalCtx.globalAlpha = 0.1
    for (let i = 0; i < 500; i++) {
      const hue = Math.random() * 60 + 210 // Blue-ish hues
      normalCtx.fillStyle = `hsl(${hue}, 50%, 60%)`
      normalCtx.fillRect(
        Math.random() * 512,
        Math.random() * 512,
        Math.random() * 4 + 2,
        Math.random() * 4 + 2
      )
    }
    
    const normalTexture = new THREE.CanvasTexture(normalCanvas)
    normalTexture.wrapS = THREE.RepeatWrapping
    normalTexture.wrapT = THREE.RepeatWrapping
    normalTexture.repeat.set(2, 2)
    
    material.normalMap = normalTexture
    material.normalScale = new THREE.Vector2(0.1, 0.1)
    
    return material
  }, [paperThickness])

  // Update material properties when store changes
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.thickness = paperThickness * 100
      
      // Animate slight color variations for realism
      const time = Date.now() * 0.0001
      const colorVariation = Math.sin(time) * 0.01
      materialRef.current.color.setRGB(
        0.99 + colorVariation,
        0.99 + colorVariation,
        0.99 + colorVariation * 0.5
      )
    }
  })

  return <primitive ref={materialRef} object={paperMaterial} />
}

// Enhanced lighting setup for realistic paper rendering
export const PaperLighting: React.FC = () => {
  const lightRef = useRef<THREE.DirectionalLight>(null)
  
  useFrame(({ clock }) => {
    if (lightRef.current) {
      // Subtle light movement for dynamic shadows
      const time = clock.getElapsedTime()
      lightRef.current.position.x = 10 + Math.sin(time * 0.1) * 2
      lightRef.current.position.z = 5 + Math.cos(time * 0.1) * 2
    }
  })

  return (
    <>
      {/* Main directional light */}
      <directionalLight
        ref={lightRef}
        position={[10, 10, 5]}
        intensity={1.2}
        castShadow
        shadow-mapSize-width={4096}
        shadow-mapSize-height={4096}
        shadow-camera-far={50}
        shadow-camera-left={-15}
        shadow-camera-right={15}
        shadow-camera-top={15}
        shadow-camera-bottom={-15}
        shadow-bias={-0.0001}
      />
      
      {/* Fill light for softer shadows */}
      <directionalLight
        position={[-5, 8, -3]}
        intensity={0.3}
        color="#f0f8ff"
      />
      
      {/* Ambient light for overall illumination */}
      <ambientLight intensity={0.2} color="#f5f5f5" />
      
      {/* Rim light for edge definition */}
      <directionalLight
        position={[0, 2, -10]}
        intensity={0.4}
        color="#fff8e1"
      />
    </>
  )
}

// Crease line visualization component
export const CreaseLines: React.FC<{ folds: any[] }> = ({ folds }) => {
  const { showCreases } = useOrigamiStore()
  
  if (!showCreases || folds.length === 0) return null

  return (
    <group>
      {folds.map((fold, index) => (
        <group key={fold.id || index}>
          {/* Main crease line */}
          <line>
            <bufferGeometry>
              <bufferAttribute
                attach="attributes-position"
                count={2}
                array={new Float32Array([
                  fold.start.x, fold.start.y, fold.start.z + 0.001,
                  fold.end.x, fold.end.y, fold.end.z + 0.001
                ])}
                itemSize={3}
              />
            </bufferGeometry>
            <lineBasicMaterial 
              color={fold.type === 'valley' ? '#ff4444' : '#4444ff'} 
              linewidth={2}
              transparent
              opacity={fold.strength * 0.8}
            />
          </line>
          
          {/* Crease shadow/depth effect */}
          <line>
            <bufferGeometry>
              <bufferAttribute
                attach="attributes-position"
                count={2}
                array={new Float32Array([
                  fold.start.x, fold.start.y, fold.start.z - 0.0005,
                  fold.end.x, fold.end.y, fold.end.z - 0.0005
                ])}
                itemSize={3}
              />
            </bufferGeometry>
            <lineBasicMaterial 
              color="#000000" 
              linewidth={1}
              transparent
              opacity={fold.strength * 0.2}
            />
          </line>
        </group>
      ))}
    </group>
  )
}

export default PaperMaterial
