import React, { useState } from 'react'
import { useOrigamiStore } from '../store/origamiStore'
import Controls from './Controls'
import OrigamiPresets, { OrigamiPreset } from './OrigamiPresets'

const UI: React.FC = () => {
  const [showPresets, setShowPresets] = useState(false)

  const {
    paperSize,
    setPaperSize,
    subdivisions,
    setSubdivisions,
    paperThickness,
    setPaperThickness,
    showCreases,
    setShowCreases,
    foldStrength,
    setFoldStrength,
    foldHistory,
    undoFold,
    redoFold,
    resetPaper,
    savePaper,
    loadPaper,
    currentMode,
    setCurrentMode,
    addFold
  } = useOrigamiStore()

  const handleApplyPreset = (preset: OrigamiPreset) => {
    // Apply each fold from the preset
    preset.folds.forEach((fold, index) => {
      setTimeout(() => {
        addFold({
          id: `preset_${preset.id}_${index}`,
          startPoint: fold.start,
          endPoint: fold.end,
          angle: fold.angle,
          type: fold.type,
          timestamp: Date.now()
        })
      }, index * 200) // Stagger the folds for visual effect
    })
  }

  return (
    <div className="ui-overlay">
      {/* Controls Panel */}
      <div className="ui-panel controls-panel">
        <h3>Paper Controls</h3>
        
        <div className="slider-container">
          <label htmlFor="paper-size">Paper Size: {paperSize.toFixed(1)}</label>
          <input
            id="paper-size"
            type="range"
            min="2"
            max="10"
            step="0.1"
            value={paperSize}
            onChange={(e) => setPaperSize(parseFloat(e.target.value))}
            className="slider"
            title="Adjust paper size"
          />
        </div>

        <div className="slider-container">
          <label htmlFor="detail-level">Detail Level: {subdivisions}</label>
          <input
            id="detail-level"
            type="range"
            min="10"
            max="100"
            step="5"
            value={subdivisions}
            onChange={(e) => setSubdivisions(parseInt(e.target.value))}
            className="slider"
            title="Adjust mesh detail level"
          />
        </div>

        <div className="slider-container">
          <label htmlFor="paper-thickness">Paper Thickness: {paperThickness.toFixed(3)}</label>
          <input
            id="paper-thickness"
            type="range"
            min="0.001"
            max="0.01"
            step="0.001"
            value={paperThickness}
            onChange={(e) => setPaperThickness(parseFloat(e.target.value))}
            className="slider"
            title="Adjust paper thickness"
          />
        </div>

        <div className="slider-container">
          <label htmlFor="fold-strength">Fold Strength: {(foldStrength * 100).toFixed(0)}%</label>
          <input
            id="fold-strength"
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={foldStrength}
            onChange={(e) => setFoldStrength(parseFloat(e.target.value))}
            className="slider"
            title="Adjust fold strength"
          />
        </div>

        <div className="slider-container">
          <label>
            <input
              type="checkbox"
              checked={showCreases}
              onChange={(e) => setShowCreases(e.target.checked)}
            />
            Show Crease Lines
          </label>
        </div>

        <div className="slider-container">
          <label htmlFor="interaction-mode">Mode:</label>
          <select
            id="interaction-mode"
            value={currentMode}
            onChange={(e) => setCurrentMode(e.target.value as 'fold' | 'view')}
            className="mode-select"
            title="Select interaction mode"
          >
            <option value="fold">Fold Mode</option>
            <option value="view">View Mode</option>
          </select>
        </div>

        <div className="slider-container">
          <button
            type="button"
            className="btn"
            onClick={() => setShowPresets(!showPresets)}
            title="Toggle origami presets"
          >
            {showPresets ? '📄 Hide Presets' : '📜 Show Presets'}
          </button>
        </div>
      </div>

      {/* Info Panel */}
      <div className="ui-panel info-panel">
        <h3>Origami Playground</h3>
        
        <div className="fold-info">
          <strong>Instructions:</strong>
        </div>
        
        <div className="fold-info">
          • <strong>Fold Mode:</strong> Click and drag to create fold lines
        </div>
        
        <div className="fold-info">
          • <strong>View Mode:</strong> Rotate and inspect your creation
        </div>
        
        <div className="fold-info">
          • Use mouse wheel to zoom in/out
        </div>
        
        <div className="fold-info">
          • Right-click and drag to pan the view
        </div>

        <div className="fold-info fold-history">
          <strong>Fold History:</strong> {foldHistory.length} folds
        </div>

        <div className="fold-info">
          <strong>Paper Properties:</strong>
        </div>
        <div className="fold-info">
          Size: {paperSize}×{paperSize} units
        </div>
        <div className="fold-info">
          Vertices: {(subdivisions + 1) * (subdivisions + 1)}
        </div>
        <div className="fold-info">
          Triangles: {subdivisions * subdivisions * 2}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="actions-panel">
        <button
          type="button"
          className="btn primary"
          onClick={undoFold}
          disabled={foldHistory.length === 0}
          title="Undo last fold"
        >
          ↶ Undo
        </button>

        <button
          type="button"
          className="btn primary"
          onClick={redoFold}
          title="Redo fold"
        >
          ↷ Redo
        </button>

        <button
          type="button"
          className="btn danger"
          onClick={resetPaper}
          title="Reset to flat paper"
        >
          🗑 Reset
        </button>

        <button
          type="button"
          className="btn"
          onClick={savePaper}
          title="Save current state"
        >
          💾 Save
        </button>

        <button
          type="button"
          className="btn"
          onClick={loadPaper}
          title="Load saved state"
        >
          📁 Load
        </button>
      </div>

      {/* Controls Component */}
      <Controls
        onUndo={undoFold}
        onRedo={redoFold}
        onReset={resetPaper}
        onSave={savePaper}
        onLoad={loadPaper}
        onToggleMode={() => setCurrentMode(currentMode === 'fold' ? 'view' : 'fold')}
      />

      {/* Origami Presets */}
      {showPresets && (
        <OrigamiPresets onApplyPreset={handleApplyPreset} />
      )}
    </div>
  )
}

export default UI
